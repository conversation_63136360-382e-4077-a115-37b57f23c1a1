import React from "react";
import { Modal } from "react-bootstrap";
import { useEditArticle } from "../Articles";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../../../utilities/redux/store";
import { updateArticle } from "../../../../../../utilities/redux/backoffice/backofficeSlice";
import ArticleForm from "../../../../../../utilities/minitiatures/ArticleForm/ArticleForm";
import { Article } from "../../../../../../utilities/constants/types";
import Button from "../../../../../../utilities/minitiatures/Button/Button";
import "./EditArticle.scss";

/**
 * Composant pour l'édition d'un article existant
 * Affiche un formulaire modal pré-rempli avec les informations de l'article
 */
const EditArticle = React.memo(() => {
    const { current, setCurrent } = useEditArticle();
    const dispatch = useDispatch<AppDispatch>();
    const submitRef = React.useRef<() => void>(null);
    const [isLoading, setIsLoading] = React.useState(false);

    // Fermeture du modal
    const handleClose = React.useCallback(() => {
        setCurrent(null);
    }, [setCurrent]);

    // Soumission du formulaire
    const handleSubmit = React.useCallback(async (articleData: Omit<Article, 'id' | 'images'> & { images: File[] }) => {
        if (!current) return;

        try {
            setIsLoading(true);

            // Préparation des données de l'article pour l'API
            const updatedArticle = {
                ...current,
                title: articleData.title,
                author: articleData.author,
                category_id: articleData.category_id,
                sections: articleData.sections,
                updated_at: new Date().toISOString(), // Sera écrasé par l'API
            };

            // Appel à l'action Redux pour mettre à jour l'article
            await dispatch(updateArticle(updatedArticle)).unwrap();

            // Fermer le modal
            handleClose();

            // Afficher un message de succès
            alert("Article mis à jour avec succès !");
        } catch (error) {
            alert("Erreur lors de la mise à jour de l'article. Veuillez réessayer.");
        } finally {
            setIsLoading(false);
        }
    }, [current, dispatch, handleClose]);

    const handleSubmitClick = React.useCallback(() => {
        if (submitRef.current) {
            submitRef.current();
        }
    }, []);

    // Vérifier si un article est sélectionné pour l'édition
    if (!current) return null;

    return (
        <Modal show={Boolean(current)} onHide={handleClose} size="xl" centered>
            <Modal.Header closeButton>
                <Modal.Title>
                    Modifier l'article
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <ArticleForm
                    onSubmit={handleSubmit}
                    initialData={current}
                    submitRef={submitRef}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button
                    type="button"
                    className="btn btn-outline-dark btn-sm"
                    onClick={handleClose}
                >
                    Annuler
                </Button>
                <Button
                    type="button"
                    className="btn btn-primary"
                    onClick={handleSubmitClick}
                    options={{ loading: isLoading }}
                >
                    <i className="fa fa-check"></i> Enregistrer
                </Button>
            </Modal.Footer>
        </Modal>
    );
});

export default EditArticle;
