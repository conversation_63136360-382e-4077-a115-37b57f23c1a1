import React from "react";
import Fade from "../../../../../utilities/minitiatures/Fade/Fade";
import ArticlesList from "./ArticlesList/ArticlesList";
import AddArticle from "./AddArticle/AddArticle";
import DeleteArticle from "./DeleteArticle/DeleteArticle";
import EditArticle from "./EditArticle/EditArticle";
import { refreshArticles, refreshCategories } from "../../../../../utilities/redux/backoffice/backofficeSlice";
import { AppDispatch, Rootstate } from "../../../../../utilities/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { Article } from "../../../../../utilities/constants/types";
import ArticlesEmpty from "./ArticlesEmpty/ArticlesEmpty";
import TablePlaceholder from "../../../../../utilities/minitiatures/TablePlaceholder/TablePlaceholder";
import ScrollEnd from "../../../../../utilities/minitiatures/ScrollEnd/ScrollEnd";
import "./Articles.scss";

// Import des scripts de migration pour le développement (Dashboard Admin)
import "../../../../../utilities/scripts/testArticleEndpoints";
import "../../../../../utilities/scripts/postBaseArticles";
import "../../../../../utilities/scripts/validateArticleStructure";
import "../../../../../utilities/scripts/checkAuthentication";
import "../../../../../utilities/scripts/fixArticleCategories";
import "../../../../../utilities/scripts/simpleArticleCreation";
import "../../../../../utilities/scripts/testNewArticleForm";



const DEFAULT_EDIT = {
    current: null as Article | null,
    setCurrent: (article: Article | null) => { article }
}

const DEFAULT_DELETE = {
    current: null as Article[] | null,
    setCurrent: (articles: Article[] | null) => { articles }
}

const ArticlesContext = React.createContext({
    edit: DEFAULT_EDIT,
    onDelete: DEFAULT_DELETE,
});

export const useEditArticle = () => {
    return React.useContext(ArticlesContext).edit;
}

export const useDeleteArticle = () => {
    return React.useContext(ArticlesContext).onDelete;
}

const Articles = React.memo(() => {

    const { articles, categories } = useSelector((state: Rootstate) => state.backoffice);
    const dispatch = useDispatch<AppDispatch>();

    const [state, setState] = React.useState({
        edit: DEFAULT_EDIT,
        onDelete: DEFAULT_DELETE,
    });

    const edit = React.useMemo(() => {
        const setCurrent = (article: Article | null) => {
            setState(s => ({ ...s, edit: { ...s.edit, current: article } }));
        }

        return {
            current: state.edit.current,
            setCurrent
        }
    }, [state.edit.current]);

    const onDelete = React.useMemo(() => {
        const setCurrent = (articles: Article[] | null) => {
            setState(s => ({ ...s, onDelete: { ...s.onDelete, current: articles } }));
        }

        return {
            current: state.onDelete.current,
            setCurrent,
        }
    }, [state.onDelete.current]);

    React.useEffect(() => {
        if (!articles) {
            dispatch(refreshArticles());
        }
    }, [articles, dispatch]);

    React.useEffect(() => {
        if (!categories) {
            dispatch(refreshCategories());
        }
    }, [categories, dispatch]);

    return <ArticlesContext.Provider value={{ edit, onDelete }}>
        <div className="articles-container">
            <Fade show={Boolean(articles && articles.length > 0)}>
                <ArticlesList />
            </Fade>
            <Fade show={Boolean(articles && articles.length === 0)}>
                <ArticlesEmpty />
            </Fade>
            <Fade show={!articles}>
                <TablePlaceholder />
            </Fade>
            <AddArticle />
            <DeleteArticle />
            <EditArticle />
            <ScrollEnd
                show={true}
                whileInView={() => dispatch(refreshArticles())}
            >
                <div></div>
            </ScrollEnd>
        </div>
    </ArticlesContext.Provider>
});

export default Articles;
