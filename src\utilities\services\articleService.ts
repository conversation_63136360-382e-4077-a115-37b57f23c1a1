/**
 * Service pour gérer les opérations CRUD sur les articles
 * Intégration avec l'API backend réelle
 *
 * Ce service gère toutes les opérations liées aux articles en utilisant
 * les endpoints de l'API backend.
 */

import { Article } from '../constants/types';
import {
  getAllArticles,
  createArticle,
  updateArticle,
  deleteArticle,
  createArticleImage,
  deleteArticleImage,
  createArticleSection,
  updateArticleSection,
  deleteArticleSection,
  createArticleSubsection,
  updateArticleSubsection,
  deleteArticleSubsection,
  createArticleParagraph,
  updateArticleParagraph,
  deleteArticleParagraph
} from '../api/actions';

/**
 * Convertit les données de l'API en format Article pour notre application
 * @param apiData - Données venant de l'API
 * @returns Article - Données au format Article
 */
const mapApiDataToArticle = (apiData: any): Article => ({
  id: apiData.id,
  title: apiData.title || '',
  author: apiData.author || '',
  category_id: apiData.category_id || null,
  created_at: apiData.created_at || new Date().toISOString(),
  updated_at: apiData.updated_at || new Date().toISOString(),
  sections: apiData.sections || [],
  images: apiData.images || []
});

/**
 * Récupère la liste des articles depuis l'API
 * @returns Promise<Article[]> - Liste des articles
 */
export const fetchArticles = async (): Promise<Article[]> => {
  try {
    // Utiliser l'action API standardisée
    const response = await getAllArticles();

    // Vérifier si la réponse contient les données attendues
    if (!response.data || !response.data.articles) {
      throw new Error('Format de réponse API inattendu');
    }

    // Mapper les données de l'API au format Article
    return response.data.articles.map(mapApiDataToArticle);
  } catch (error) {
    console.error('Erreur lors de la récupération des articles:', error);
    throw error;
  }
};

/**
 * Ajoute un nouvel article via l'API
 * @param article - Données de l'article à ajouter (sans ID, avec images en tant que File[])
 * @returns Promise<Article> - Article ajouté (avec ID généré)
 */
export const addArticle = async (article: Omit<Article, 'id' | 'images'> & { images: File[] }): Promise<Article> => {
  try {
    // Préparation des données pour l'API
    const articleData = {
      title: article.title,
      author: article.author,
      category_id: article.category_id,
      sections: article.sections,
      images: article.images // Les images sont déjà des File[]
    };

    // Utiliser l'action API standardisée
    const response = await createArticle(articleData);

    // Vérifier si la réponse contient les données attendues
    if (!response.data || !response.data.article) {
      throw new Error('Format de réponse API inattendu');
    }

    // Retourner l'article créé
    return mapApiDataToArticle(response.data.article);
  } catch (error: any) {
    throw error;
  }
};

/**
 * Met à jour un article existant via l'API
 * @param article - Données de l'article à mettre à jour (avec ID)
 * @returns Promise<Article> - Article mis à jour
 */
export const updateArticleById = async (article: Article): Promise<Article> => {
  try {
    // Préparation des données pour l'API
    const articleData = {
      id: article.id,
      title: article.title,
      author: article.author,
      category_id: article.category_id,
      sections: article.sections,
      images: [] // TODO: Gérer la mise à jour des images
    };

    // Utiliser l'action API standardisée
    const response = await updateArticle(articleData);

    // Vérifier si la réponse contient les données attendues
    if (!response.data || !response.data.article) {
      throw new Error('Format de réponse API inattendu');
    }

    // Retourner l'article mis à jour
    return mapApiDataToArticle(response.data.article);
  } catch (error) {
    throw error;
  }
};

/**
 * Supprime un article via l'API
 * @param id - ID de l'article à supprimer
 * @returns Promise<void>
 */
export const deleteArticleById = async (id: number): Promise<void> => {
  try {
    // Utiliser l'action API standardisée
    const response = await deleteArticle(id);

    // Vérifier si la suppression a réussi
    if (!response.data || response.data.status !== 'success') {
      throw new Error('Échec de la suppression de l\'article');
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Supprime plusieurs articles via l'API
 * @param ids - Tableau d'IDs des articles à supprimer
 * @returns Promise<void>
 */
export const deleteMultipleArticles = async (ids: number[]): Promise<void> => {
  try {
    // Supprimer chaque article individuellement
    await Promise.all(ids.map(id => deleteArticle(id)));
  } catch (error) {
    throw error;
  }
};

// Fonctions utilitaires pour la gestion des sections, sous-sections et paragraphes

/**
 * Ajoute une image à un article
 */
export const addImageToArticle = async (articleId: number, image: File, caption?: string) => {
  try {
    const response = await createArticleImage({
      article_id: articleId,
      image,
      caption
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'ajout d\'image:', error);
    throw error;
  }
};

/**
 * Supprime une image d'un article
 */
export const removeImageFromArticle = async (imageId: number) => {
  try {
    await deleteArticleImage(imageId);
  } catch (error) {
    console.error('Erreur lors de la suppression d\'image:', error);
    throw error;
  }
};

/**
 * Ajoute une section à un article
 */
export const addSectionToArticle = async (articleId: number, title: string, order?: number) => {
  try {
    const response = await createArticleSection({
      article_id: articleId,
      title,
      order
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'ajout de section:', error);
    throw error;
  }
};

/**
 * Met à jour une section
 */
export const updateSectionById = async (sectionId: number, title: string, order?: number) => {
  try {
    const response = await updateArticleSection(sectionId, { title, order });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour de section:', error);
    throw error;
  }
};

/**
 * Supprime une section
 */
export const removeSectionFromArticle = async (sectionId: number) => {
  try {
    await deleteArticleSection(sectionId);
  } catch (error) {
    console.error('Erreur lors de la suppression de section:', error);
    throw error;
  }
};

/**
 * Ajoute une sous-section à une section
 */
export const addSubsectionToSection = async (sectionId: number, title: string, order?: number) => {
  try {
    const response = await createArticleSubsection({
      section_id: sectionId,
      title,
      order
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'ajout de sous-section:', error);
    throw error;
  }
};

/**
 * Met à jour une sous-section
 */
export const updateSubsectionById = async (subsectionId: number, title: string, order?: number) => {
  try {
    const response = await updateArticleSubsection(subsectionId, { title, order });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour de sous-section:', error);
    throw error;
  }
};

/**
 * Supprime une sous-section
 */
export const removeSubsectionFromSection = async (subsectionId: number) => {
  try {
    await deleteArticleSubsection(subsectionId);
  } catch (error) {
    console.error('Erreur lors de la suppression de sous-section:', error);
    throw error;
  }
};

/**
 * Ajoute un paragraphe à une sous-section
 */
export const addParagraphToSubsection = async (sectionId: number, content: string, order?: number) => {
  try {
    const response = await createArticleParagraph({
      section_id: sectionId,
      content,
      order
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'ajout de paragraphe:', error);
    throw error;
  }
};

/**
 * Met à jour un paragraphe
 */
export const updateParagraphById = async (paragraphId: number, content: string, order?: number) => {
  try {
    const response = await updateArticleParagraph(paragraphId, { content, order });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour de paragraphe:', error);
    throw error;
  }
};

/**
 * Supprime un paragraphe
 */
export const removeParagraphFromSubsection = async (paragraphId: number) => {
  try {
    await deleteArticleParagraph(paragraphId);
  } catch (error) {
    console.error('Erreur lors de la suppression de paragraphe:', error);
    throw error;
  }
};
